import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import glob

# Set font to Times New Roman for IEEE journal requirements
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12

# Font size configuration - Easy to adjust all text sizes
FONT_CONFIG = {
    'axis_labels': 20,      # X and Y axis labels (SNR (dB), Accuracy)
    'legend': 18,           # Legend text
    'tick_labels': 18,      # Main plot tick labels (numbers on axes)
    'inset_ticks': 12       # Inset (zoom box) tick labels
}

# Configuration
INPUT_FOLDER = 'snr_results_tables'
OUTPUT_FOLDER = 'snr_plots'

# Dataset selection - Specify which datasets to process
DATASET_CONFIG = {
    'datasets': ['rml'],     # Options: 'rml', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'
    'enable_zoom_rml': True,          # Enable zoom inset for RML dataset
    'enable_zoom_hisar': True,        # Enable zoom inset for HISAR dataset
    'enable_zoom_torchsig1024': False, # TorchSig datasets don't need zoom
    'enable_zoom_torchsig2048': False,
    'enable_zoom_torchsig4096': False
}

# Model name mapping - Map original names to display names
MODEL_NAME_MAPPING = {
    'WNN-MRNN': 'MWRNN',  # Map WNN-MRNN to MWRNN for display
    # Add more mappings here if needed
    # 'Original Name': 'Display Name'
}

# Plot styling configuration - Top journal color scheme for clear distinction
PLOT_STYLES = {
    'CLDNN': {'marker': 'D', 'linestyle': ':', 'color': '#1f77b4'},      # Blue
    'AWN': {'marker': 's', 'linestyle': '--', 'color': '#ff7f0e'},       # Orange
    'MAMC': {'marker': '<', 'linestyle': '--', 'color': '#2ca02c'},      # Green
    'MAWDN': {'marker': '>', 'linestyle': '-.', 'color': '#9467bd'},     # Purple
    'WNN-MRNN': {'marker': 'o', 'linestyle': '-', 'color': '#d62728'}    # Red
}

# Inset (zoom box) configuration - Easy to adjust
INSET_CONFIG = {
    'rml': {
        'x_position': 0.55,    # X position of inset box (0-1, left to right)
        'y_position': 0.19,    # Y position of inset box (0-1, bottom to top)
        'width': 0.44,         # Width of inset box (0-1)
        'height': 0.35,        # Height of inset box (0-1)
        'zoom_range': [6, 18.5], # SNR range to zoom into [min_snr, max_snr]
        'border_width': 1.5,   # Width of the connecting lines and border
        'border_alpha': 0.8    # Transparency of the connecting lines and border
    },
    'hisar': {
        'x_position': 0.55,    # X position of inset box (0-1, left to right)
        'y_position': 0.05,    # Y position of inset box (0-1, bottom to top)
        'width': 0.44,         # Width of inset box (0-1)
        'height': 0.35,        # Height of inset box (0-1)
        'zoom_range': [10, 18.5], # SNR range to zoom into [min_snr, max_snr]
        'border_width': 1.5,   # Width of the connecting lines and border
        'border_alpha': 0.8    # Transparency of the connecting lines and border
    }
}

def create_output_folder():
    """Create output folder for plots"""
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"Created output folder: {OUTPUT_FOLDER}")
    else:
        print(f"Output folder already exists: {OUTPUT_FOLDER}")

def get_available_files():
    """Get list of available Excel files"""
    pattern = os.path.join(INPUT_FOLDER, "snr_results_*.xlsx")
    files = glob.glob(pattern)
    return [os.path.basename(f) for f in files]

def extract_dataset_name(filename):
    """Extract dataset name from filename"""
    # Remove 'snr_results_' prefix and '.xlsx' suffix
    return filename.replace('snr_results_', '').replace('.xlsx', '')

def parse_snr_value(snr_str):
    """Parse SNR string to numeric value"""
    try:
        # Remove 'dB' suffix and convert to float
        return float(snr_str.replace('dB', ''))
    except:
        return None

def plot_single_dataset(excel_file, selected_models=None, enable_zoom=True, dataset_name='rml'):
    """Plot SNR vs Accuracy for a single dataset

    Args:
        excel_file: Excel file name
        selected_models: List of models to plot (None for all)
        enable_zoom: Whether to add zoom inset (default True)
        dataset_name: Name of dataset ('rml' or 'hisar') for configuration
    """

    # Read Excel file
    file_path = os.path.join(INPUT_FOLDER, excel_file)
    df = pd.read_excel(file_path)

    # Extract dataset name
    dataset_name = extract_dataset_name(excel_file)

    # Get SNR columns (all columns except 'Model Name')
    snr_columns = [col for col in df.columns if col != 'Model Name']

    # Parse SNR values
    snr_values = []
    for col in snr_columns:
        snr_val = parse_snr_value(col)
        if snr_val is not None:
            snr_values.append(snr_val)

    if not snr_values:
        print(f"Warning: No valid SNR values found in {excel_file}")
        return

    # Sort SNR values
    snr_indices = np.argsort(snr_values)
    sorted_snr_values = [snr_values[i] for i in snr_indices]
    sorted_snr_columns = [snr_columns[i] for i in snr_indices]

    # Create plot with IEEE journal formatting (4:3 ratio)
    plt.figure(figsize=(8, 6))
    
    # Plot each model
    for _, row in df.iterrows():
        model_name = row['Model Name']

        # Skip if model not in selected models (when specified)
        if selected_models and model_name not in selected_models:
            continue

        # Extract accuracy values
        accuracies = []
        valid_snr_values = []

        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            acc_val = row[snr_col]
            if pd.notna(acc_val) and acc_val != '':
                try:
                    # Convert percentage to decimal (0-1 range)
                    acc_decimal = float(acc_val) / 100.0
                    accuracies.append(acc_decimal)
                    valid_snr_values.append(snr_val)
                except:
                    continue

        if not accuracies:
            print(f"Warning: No valid accuracy data for model {model_name}")
            continue

        # Map model name for display (if mapping exists)
        display_name = MODEL_NAME_MAPPING.get(model_name, model_name)

        # Debug: Print mapping information
        print(f"DEBUG: Mapped to display name: '{display_name}'")
        print(f"DEBUG: Available styles: {list(PLOT_STYLES.keys())}")
        print(f"DEBUG: Display name in styles? {display_name in PLOT_STYLES}")

        # Get plot style using display name
        style = PLOT_STYLES.get(display_name, {'marker': 'o', 'linestyle': '-', 'color': 'black'})
        print(f"DEBUG: Using style: {style}")
        print("-" * 50)

        # Plot line
        plt.plot(valid_snr_values, accuracies,
                marker=style['marker'],
                linestyle=style['linestyle'],
                color=style['color'],
                label=display_name,  # Use display name for legend
                linewidth=2,
                markersize=6)

    # Customize plot for IEEE journal requirements
    plt.xlabel('SNR (dB)', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    plt.ylabel('Accuracy', fontsize=FONT_CONFIG['axis_labels'], fontfamily='Times New Roman')
    # Remove title as requested
    plt.grid(True, alpha=0.3)
    # Place legend - special position for torchsig4096
    if dataset_name.lower() == 'torchsig4096':
        plt.legend(loc='lower right', fontsize=FONT_CONFIG['legend'], frameon=True, fancybox=False, shadow=False)
    else:
        plt.legend(loc='upper left', fontsize=FONT_CONFIG['legend'], frameon=True, fancybox=False, shadow=False)

    # Set y-axis limits and format (0.1, 0.2 format instead of percentage)
    dataset_lower = dataset_name.lower()
    if dataset_lower == 'hisar':
        plt.ylim(0.35, 1.05)  # HISAR starts from 0.35
    elif dataset_lower.startswith('torchsig'):
        plt.ylim(0, 1.05)     # TorchSig datasets start from 0
    else:
        plt.ylim(0, 1.05)     # RML and others start from 0
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1f}'.format(y)))

    # Set tick parameters for IEEE journal style
    plt.tick_params(axis='both', which='major', labelsize=FONT_CONFIG['tick_labels'])

    # Create inset axes for zoomed region (only if enabled)
    if enable_zoom:
        # Get configuration for the specific dataset
        inset_config = INSET_CONFIG[dataset_name.lower()]
        # Position: [x0, y0, width, height] in axes coordinates
        axins = plt.gca().inset_axes([inset_config['x_position'],
                                      inset_config['y_position'],
                                      inset_config['width'],
                                      inset_config['height']])

        # Plot the same data in the inset axes
        for _, row in df.iterrows():
            model_name = row['Model Name']

            # Skip if model not in selected models (when specified)
            if selected_models and model_name not in selected_models:
                continue

            # Extract accuracy values for zoom region
            zoom_accuracies = []
            zoom_snr_values = []

            for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
                # Only include SNR values in the configurable zoom range
                if inset_config['zoom_range'][0] <= snr_val <= inset_config['zoom_range'][1]:
                    acc_val = row[snr_col]
                    if pd.notna(acc_val) and acc_val != '':
                        try:
                            acc_decimal = float(acc_val) / 100.0
                            zoom_accuracies.append(acc_decimal)
                            zoom_snr_values.append(snr_val)
                        except:
                            continue

            if zoom_accuracies:
                # Map model name for display (if mapping exists)
                display_name = MODEL_NAME_MAPPING.get(model_name, model_name)

                # Get plot style using display name
                style = PLOT_STYLES.get(display_name, {'marker': 'o', 'linestyle': '-', 'color': 'black'})

                # Plot in inset axes
                axins.plot(zoom_snr_values, zoom_accuracies,
                          marker=style['marker'],
                          linestyle=style['linestyle'],
                          color=style['color'],
                          linewidth=2,
                          markersize=4)

        # Set zoom region limits using configurable range
        axins.set_xlim(inset_config['zoom_range'][0], inset_config['zoom_range'][1])

        # Get y-axis range for the zoom region and add padding
        if len(df) > 0:
            # Calculate min and max accuracy values in the zoom region
            zoom_y_values = []
            for _, row in df.iterrows():
                model_name = row['Model Name']
                if selected_models and model_name not in selected_models:
                    continue
                for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
                    if inset_config['zoom_range'][0] <= snr_val <= inset_config['zoom_range'][1]:
                        acc_val = row[snr_col]
                        if pd.notna(acc_val) and acc_val != '':
                            try:
                                acc_decimal = float(acc_val) / 100.0
                                zoom_y_values.append(acc_decimal)
                            except:
                                continue

            if zoom_y_values:
                y_min = min(zoom_y_values)
                y_max = max(zoom_y_values)
                y_range = y_max - y_min
                # Add 15% padding above and below
                padding = y_range * 0.15
                axins.set_ylim(y_min - padding, y_max + padding)
            else:
                axins.set_ylim(auto=True)
        else:
            axins.set_ylim(auto=True)

        # Format inset axes with 2 decimal places for y-axis
        axins.grid(True, alpha=0.3)
        axins.tick_params(axis='both', which='major', labelsize=FONT_CONFIG['inset_ticks'])
        axins.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.2f}'.format(y)))

        # Set x-axis ticks (step by 2, no odd numbers) for both datasets
        x_min, x_max = inset_config['zoom_range']
        # Generate even numbers within the range
        x_ticks = []
        start = int(x_min)
        if start % 2 != 0:  # If start is odd, make it even
            start += 1
        end = int(x_max) + 1
        for x in range(start, end, 2):  # Step by 2
            if x_min <= x <= x_max:
                x_ticks.append(x)
        axins.set_xticks(x_ticks)

        # Add rectangle and connecting lines to show zoom region using configurable parameters
        plt.gca().indicate_inset_zoom(axins, edgecolor='black',
                                      linewidth=inset_config['border_width'],
                                      alpha=inset_config['border_alpha'])

    # Tight layout
    plt.tight_layout()

    # Save plot in both PNG and PDF formats
    # PNG format (high resolution for viewing)
    png_filename = f"snr_accuracy_{dataset_name}.png"
    png_path = os.path.join(OUTPUT_FOLDER, png_filename)
    plt.savefig(png_path, dpi=300, bbox_inches='tight')
    print(f"Saved PNG plot: {png_path}")

    # PDF format (vector format for publications)
    pdf_filename = f"snr_accuracy_{dataset_name}.pdf"
    pdf_path = os.path.join(OUTPUT_FOLDER, pdf_filename)
    plt.savefig(pdf_path, format='pdf', bbox_inches='tight')
    print(f"Saved PDF plot: {pdf_path}")

    # Close the plot to free memory
    plt.close()

# Interactive functions removed - focusing on RML dataset only

def main():
    """Main function - Process datasets based on configuration"""
    print("=" * 60)
    print("SNR Results Plotting Tool")
    print("=" * 60)

    # Create output folder
    create_output_folder()

    datasets_to_process = DATASET_CONFIG['datasets']

    # Validate dataset list
    valid_datasets = ['rml', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096']
    for dataset in datasets_to_process:
        if dataset.lower() not in valid_datasets:
            print(f"Error: Invalid dataset '{dataset}'. Valid options: {valid_datasets}")
            return

    if not datasets_to_process:
        print("Error: No datasets specified in DATASET_CONFIG['datasets']")
        return

    print(f"Processing datasets: {datasets_to_process}")
    print("-" * 60)

    # Process each specified dataset
    for dataset in datasets_to_process:
        dataset_lower = dataset.lower()

        if dataset_lower == 'rml':
            # Process RML dataset
            rml_file = "snr_results_rml.xlsx"
            rml_path = os.path.join(INPUT_FOLDER, rml_file)

            if not os.path.exists(rml_path):
                print(f"Warning: RML dataset file not found: {rml_path}")
                print("Please ensure the file 'snr_results_rml.xlsx' exists in the input folder.")
                continue

            print(f"Processing RML dataset with all models...")
            enable_zoom = DATASET_CONFIG['enable_zoom_rml']
            plot_single_dataset(rml_file, selected_models=None, enable_zoom=enable_zoom, dataset_name='rml')
            print(f"RML plot saved to: {OUTPUT_FOLDER}")

        elif dataset_lower == 'hisar':
            # Process HISAR dataset
            hisar_file = "snr_results_hisar.xlsx"
            hisar_path = os.path.join(INPUT_FOLDER, hisar_file)

            if not os.path.exists(hisar_path):
                print(f"Warning: HISAR dataset file not found: {hisar_path}")
                print("Please ensure the file 'snr_results_hisar.xlsx' exists in the input folder.")
                continue

            print(f"Processing HISAR dataset with all models...")
            enable_zoom = DATASET_CONFIG['enable_zoom_hisar']
            plot_single_dataset(hisar_file, selected_models=None, enable_zoom=enable_zoom, dataset_name='hisar')
            print(f"HISAR plot saved to: {OUTPUT_FOLDER}")

        elif dataset_lower.startswith('torchsig'):
            # Process TorchSig datasets (1024, 2048, 4096)
            torchsig_file = f"snr_results_{dataset_lower}.xlsx"
            torchsig_path = os.path.join(INPUT_FOLDER, torchsig_file)

            if not os.path.exists(torchsig_path):
                print(f"Warning: {dataset_lower.upper()} dataset file not found: {torchsig_path}")
                print(f"Please ensure the file '{torchsig_file}' exists in the input folder.")
                continue

            print(f"Processing {dataset_lower.upper()} dataset with all models...")
            enable_zoom = DATASET_CONFIG.get(f'enable_zoom_{dataset_lower}', False)
            plot_single_dataset(torchsig_file, selected_models=None, enable_zoom=enable_zoom, dataset_name=dataset_lower)
            print(f"{dataset_lower.upper()} plot saved to: {OUTPUT_FOLDER}")

        print("-" * 40)

    print(f"\n=" * 60)
    print(f"All plots saved to: {OUTPUT_FOLDER}")
    print("=" * 60)

if __name__ == '__main__':
    main()
